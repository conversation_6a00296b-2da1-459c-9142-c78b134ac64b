Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.26f1c1 (19b503b0ef33) revision 1684739'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 65368 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.26f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest
-logFile
Logs/AssetImportWorker1.log
-srvPort
7772
Successfully changed project path to: E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest
E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [5964] Host "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3585239737 [EditorId] 3585239737 [Version] 1048832 [Id] WindowsEditor(7,Westrice) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [5964] Host "[IP] ************** [Port] 0 [Flags] 2 [Guid] 3585239737 [EditorId] 3585239737 [Version] 1048832 [Id] WindowsEditor(7,Westrice) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 55.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.26f1c1 (19b503b0ef33)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     24142 MB
    Driver:   32.0.15.6590
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56220
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.26f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002496 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 224 ms
Refreshing native plugins compatible for Editor in 55.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.609 seconds
Domain Reload Profiling:
	ReloadAssembly (609ms)
		BeginReloadAssembly (46ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (510ms)
			LoadAssemblies (45ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (60ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (403ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (274ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (55ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (48ms)
				ProcessInitializeOnLoadMethodAttributes (24ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.005894 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 60.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.650 seconds
Domain Reload Profiling:
	ReloadAssembly (651ms)
		BeginReloadAssembly (67ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (11ms)
		EndReloadAssembly (527ms)
			LoadAssemblies (51ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (61ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (130ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4012 Unused Serialized files (Serialized files now loaded: 0)
Unloading 35 unused Assets / (286.7 KB). Loaded Objects now: 4497.
Memory consumption went from 151.4 MB to 151.1 MB.
Total: 1.706000 ms (FindLiveObjects: 0.166200 ms CreateObjectMapping: 0.047700 ms MarkObjects: 1.426000 ms  DeleteObjects: 0.065400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005882 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.544 seconds
Domain Reload Profiling:
	ReloadAssembly (544ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (413ms)
			LoadAssemblies (48ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (171ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (106ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4501.
Memory consumption went from 145.3 MB to 145.2 MB.
Total: 1.693000 ms (FindLiveObjects: 0.170100 ms CreateObjectMapping: 0.049000 ms MarkObjects: 1.454000 ms  DeleteObjects: 0.018800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.008039 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.547 seconds
Domain Reload Profiling:
	ReloadAssembly (548ms)
		BeginReloadAssembly (87ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (410ms)
			LoadAssemblies (51ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (167ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (101ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.8 KB). Loaded Objects now: 4505.
Memory consumption went from 145.3 MB to 145.2 MB.
Total: 1.709100 ms (FindLiveObjects: 0.153600 ms CreateObjectMapping: 0.051200 ms MarkObjects: 1.487700 ms  DeleteObjects: 0.015600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006567 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.563 seconds
Domain Reload Profiling:
	ReloadAssembly (563ms)
		BeginReloadAssembly (81ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (19ms)
		EndReloadAssembly (413ms)
			LoadAssemblies (56ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (146ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (21ms)
			SetupLoadedEditorAssemblies (164ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (39ms)
				ProcessInitializeOnLoadAttributes (96ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4509.
Memory consumption went from 145.3 MB to 145.2 MB.
Total: 1.677600 ms (FindLiveObjects: 0.178800 ms CreateObjectMapping: 0.052900 ms MarkObjects: 1.431900 ms  DeleteObjects: 0.013400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.008189 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.542 seconds
Domain Reload Profiling:
	ReloadAssembly (542ms)
		BeginReloadAssembly (85ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (406ms)
			LoadAssemblies (53ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (141ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (21ms)
			SetupLoadedEditorAssemblies (169ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (38ms)
				ProcessInitializeOnLoadAttributes (101ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (6ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4513.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.585900 ms (FindLiveObjects: 0.157200 ms CreateObjectMapping: 0.047700 ms MarkObjects: 1.366800 ms  DeleteObjects: 0.013200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.008375 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.541 seconds
Domain Reload Profiling:
	ReloadAssembly (542ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (409ms)
			LoadAssemblies (52ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (138ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (24ms)
			SetupLoadedEditorAssemblies (172ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (106ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4517.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.733000 ms (FindLiveObjects: 0.157900 ms CreateObjectMapping: 0.046800 ms MarkObjects: 1.512900 ms  DeleteObjects: 0.014600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005700 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.542 seconds
Domain Reload Profiling:
	ReloadAssembly (542ms)
		BeginReloadAssembly (83ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (409ms)
			LoadAssemblies (49ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (142ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (21ms)
			SetupLoadedEditorAssemblies (176ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (102ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (26.7 KB). Loaded Objects now: 4521.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.555300 ms (FindLiveObjects: 0.146400 ms CreateObjectMapping: 0.050800 ms MarkObjects: 1.341500 ms  DeleteObjects: 0.015800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005562 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.545 seconds
Domain Reload Profiling:
	ReloadAssembly (546ms)
		BeginReloadAssembly (83ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (412ms)
			LoadAssemblies (52ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (143ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (23ms)
			SetupLoadedEditorAssemblies (169ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (102ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4525.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.634700 ms (FindLiveObjects: 0.154600 ms CreateObjectMapping: 0.048600 ms MarkObjects: 1.416000 ms  DeleteObjects: 0.014800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.007869 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.560 seconds
Domain Reload Profiling:
	ReloadAssembly (561ms)
		BeginReloadAssembly (82ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (427ms)
			LoadAssemblies (53ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (150ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (178ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (103ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.6 KB). Loaded Objects now: 4529.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.606300 ms (FindLiveObjects: 0.154200 ms CreateObjectMapping: 0.053000 ms MarkObjects: 1.383900 ms  DeleteObjects: 0.014500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.007801 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.542 seconds
Domain Reload Profiling:
	ReloadAssembly (543ms)
		BeginReloadAssembly (85ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (407ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (139ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (171ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (37ms)
				ProcessInitializeOnLoadAttributes (105ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.6 KB). Loaded Objects now: 4533.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 2.046400 ms (FindLiveObjects: 0.152500 ms CreateObjectMapping: 0.045400 ms MarkObjects: 1.831800 ms  DeleteObjects: 0.015900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006428 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.535 seconds
Domain Reload Profiling:
	ReloadAssembly (536ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (403ms)
			LoadAssemblies (52ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (135ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (172ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (36ms)
				ProcessInitializeOnLoadAttributes (106ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (26.7 KB). Loaded Objects now: 4537.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 2.103100 ms (FindLiveObjects: 0.155700 ms CreateObjectMapping: 0.052700 ms MarkObjects: 1.878300 ms  DeleteObjects: 0.015300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005604 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.543 seconds
Domain Reload Profiling:
	ReloadAssembly (543ms)
		BeginReloadAssembly (84ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (20ms)
		EndReloadAssembly (408ms)
			LoadAssemblies (51ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (143ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (22ms)
			SetupLoadedEditorAssemblies (169ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (38ms)
				ProcessInitializeOnLoadAttributes (100ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (6ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4541.
Memory consumption went from 145.3 MB to 145.3 MB.
Total: 1.804900 ms (FindLiveObjects: 0.154900 ms CreateObjectMapping: 0.068200 ms MarkObjects: 1.565700 ms  DeleteObjects: 0.015400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.008175 seconds.
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll
Symbol file LoadedFromMemory doesn't match image E:\Unity Project\OHA_CursorProject_git\OHA_Pro_CursorTest\Library\PackageCache\com.unity.visualscripting@1.8.0\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll
Symbol file LoadedFromMemory is not a mono symbol file
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
测试报告导出工具已初始化 - 报告将保存到: Assets/Tests/Reports
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
TestReportExporter:.cctor () (at Assets/Tests/Editor/TestReportExporter.cs:24)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Assets/Tests/Editor/TestReportExporter.cs Line: 24)

Mono: successfully reloaded assembly
- Completed reload, in  0.543 seconds
Domain Reload Profiling:
	ReloadAssembly (544ms)
		BeginReloadAssembly (87ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (22ms)
		EndReloadAssembly (407ms)
			LoadAssemblies (52ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (147ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (24ms)
			SetupLoadedEditorAssemblies (160ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (36ms)
				ProcessInitializeOnLoadAttributes (95ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3959 Unused Serialized files (Serialized files now loaded: 0)
Unloading 27 unused Assets / (25.7 KB). Loaded Objects now: 4545.
Memory consumption went from 145.4 MB to 145.4 MB.
Total: 1.734800 ms (FindLiveObjects: 0.143400 ms CreateObjectMapping: 0.046600 ms MarkObjects: 1.495500 ms  DeleteObjects: 0.048400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
